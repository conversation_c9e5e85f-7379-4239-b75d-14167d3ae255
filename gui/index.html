<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZMT Compression Platform</title>
    <meta name="description" content="Professional ZMT compression and decompression platform with intuitive GUI">
    <link rel="stylesheet" href="/static/styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🗜️</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1 class="title">🗜️ ZMT Compression Platform</h1>
                <div class="status-indicator">
                    <span class="status-dot" id="statusDot"></span>
                    <span class="status-text" id="statusText">Checking...</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Tab Navigation -->
            <nav class="tab-nav">
                <button class="tab-btn active" data-tab="compress">🗜️ Compress</button>
                <button class="tab-btn" data-tab="decompress">📦 Decompress</button>
                <button class="tab-btn" data-tab="archive">📋 Archive Info</button>
            </nav>

            <!-- Compress Tab -->
            <div class="tab-content active" id="compress-tab">
                <div class="section">
                    <h2>Compress Files</h2>
                    
                    <!-- Upload Mode -->
                    <div class="upload-section">
                        <h3>Upload Files</h3>
                        <div class="drop-zone" id="compressDropZone">
                            <div class="drop-zone-content">
                                <span class="drop-icon">📁</span>
                                <p>Drag & drop files here or <button type="button" class="link-btn" id="browseFilesBtn">browse</button></p>
                                <input type="file" id="fileInput" multiple hidden>
                            </div>
                        </div>
                        <div class="file-list" id="selectedFiles"></div>
                    </div>

                    <!-- OR Separator -->
                    <div class="separator">
                        <span>OR use server path</span>
                    </div>

                    <!-- Local Path Mode -->
                    <div class="path-section">
                        <h3>Local Server Path</h3>
                        <p class="help-text">💡 Enter the full server path or use Browse to identify the folder<br>
                        <small>Tip: Browse will identify the folder name - you'll need to complete the full path</small></p>
                        <div class="input-group">
                            <input type="text" id="localPath" placeholder="Enter file or directory path, or use Browse" class="path-input">
                            <button type="button" id="browsePathBtn" class="btn btn-secondary">📁 Browse</button>
                            <input type="file" id="pathFileInput" hidden>
                            <input type="file" id="pathDirInput" webkitdirectory hidden>
                        </div>
                    </div>

                    <!-- Options -->
                    <div class="options-section">
                        <h3>Options</h3>
                        <div class="options-grid">
                            <label class="checkbox-label">
                                <input type="checkbox" id="maxCompress">
                                <span class="checkmark"></span>
                                Maximum Compression
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="deleteOriginal">
                                <span class="checkmark"></span>
                                Delete Original
                            </label>
                        </div>
                        <div class="input-group">
                            <label for="outputFilename">Custom Output Filename (optional):</label>
                            <input type="text" id="outputFilename" placeholder="archive.zmt" class="text-input">
                        </div>
                        <div class="input-group">
                            <label for="outputPath">Output Directory (optional):</label>
                            <input type="text" id="outputPath" placeholder="Leave empty for default, or use Browse to identify folder" class="text-input">
                            <button type="button" id="browseOutputPathBtn" class="btn btn-secondary">📁 Browse</button>
                            <input type="file" id="outputPathInput" webkitdirectory hidden>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="action-section">
                        <button type="button" id="compressBtn" class="btn btn-primary">
                            <span class="btn-text">Compress Files</span>
                            <span class="btn-spinner" hidden>⏳</span>
                        </button>
                    </div>

                    <!-- Progress -->
                    <div class="progress-section" id="compressProgress" hidden>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p class="progress-text">Compressing files...</p>
                    </div>

                    <!-- Results -->
                    <div class="results-section" id="compressResults" hidden>
                        <h3>Compression Results</h3>
                        <div class="results-grid" id="compressResultsContent"></div>
                    </div>
                </div>
            </div>

            <!-- Decompress Tab -->
            <div class="tab-content" id="decompress-tab">
                <div class="section">
                    <h2>Decompress Archive</h2>
                    
                    <!-- Upload Mode -->
                    <div class="upload-section">
                        <h3>Upload Archive</h3>
                        <div class="drop-zone" id="decompressDropZone">
                            <div class="drop-zone-content">
                                <span class="drop-icon">📦</span>
                                <p>Drag & drop .zmt archive here or <button type="button" class="link-btn" id="browseArchiveBtn">browse</button></p>
                                <input type="file" id="archiveInput" accept=".zmt" hidden>
                            </div>
                        </div>
                        <div class="file-list" id="selectedArchive"></div>
                    </div>

                    <!-- OR Separator -->
                    <div class="separator">
                        <span>OR</span>
                    </div>

                    <!-- Local Path Mode -->
                    <div class="path-section">
                        <h3>Local Archive Path</h3>
                        <p class="help-text">💡 Enter the full server path to the .zmt archive, or use Browse to select and upload the archive instead</p>
                        <div class="input-group">
                            <input type="text" id="archivePath" placeholder="Enter .zmt archive path" class="path-input">
                            <button type="button" id="browseArchivePathBtn" class="btn btn-secondary">📦 Browse</button>
                            <input type="file" id="archivePathInput" accept=".zmt" hidden>
                        </div>
                    </div>

                    <!-- Output Directory -->
                    <div class="options-section">
                        <h3>Output Options</h3>
                        <div class="input-group">
                            <label for="outputDir">Custom Output Directory (optional):</label>
                            <input type="text" id="outputDir" placeholder="Leave empty for default, or use Browse to identify folder" class="text-input">
                            <button type="button" id="browseOutputDirBtn" class="btn btn-secondary">📁 Browse</button>
                            <input type="file" id="outputDirInput" webkitdirectory hidden>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="action-section">
                        <button type="button" id="decompressBtn" class="btn btn-primary">
                            <span class="btn-text">Decompress Archive</span>
                            <span class="btn-spinner" hidden>⏳</span>
                        </button>
                    </div>

                    <!-- Progress -->
                    <div class="progress-section" id="decompressProgress" hidden>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p class="progress-text">Decompressing archive...</p>
                    </div>

                    <!-- Results -->
                    <div class="results-section" id="decompressResults" hidden>
                        <h3>Decompression Results</h3>
                        <div class="results-grid" id="decompressResultsContent"></div>
                    </div>
                </div>
            </div>

            <!-- Archive Info Tab -->
            <div class="tab-content" id="archive-tab">
                <div class="section">
                    <h2>Archive Information</h2>
                    
                    <!-- Archive Path -->
                    <div class="path-section">
                        <h3>Archive Path</h3>
                        <p class="help-text">💡 Enter the full server path to the .zmt archive you want to inspect</p>
                        <div class="input-group">
                            <input type="text" id="infoArchivePath" placeholder="Enter .zmt archive path" class="path-input">
                            <button type="button" id="browseInfoArchiveBtn" class="btn btn-secondary">📦 Browse</button>
                            <input type="file" id="infoArchiveInput" accept=".zmt" hidden>
                        </div>
                    </div>

                    <!-- Action Button -->
                    <div class="action-section">
                        <button type="button" id="listContentsBtn" class="btn btn-primary">
                            <span class="btn-text">List Contents</span>
                            <span class="btn-spinner" hidden>⏳</span>
                        </button>
                    </div>

                    <!-- Progress -->
                    <div class="progress-section" id="listProgress" hidden>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p class="progress-text">Reading archive...</p>
                    </div>

                    <!-- Results -->
                    <div class="results-section" id="archiveResults" hidden>
                        <h3>Archive Contents</h3>
                        <div class="archive-contents" id="archiveContentsTree"></div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-content">
                <div class="status-messages" id="statusMessages"></div>
                <div class="footer-info">
                    <div class="api-info">
                        <span>API: <span id="apiEndpoint">http://localhost:8000</span></span>
                    </div>
                    <div class="shortcuts-info">
                        <span>Shortcuts: Ctrl+1/2/3 (tabs) | Ctrl+Enter (execute) | Esc (clear)</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
