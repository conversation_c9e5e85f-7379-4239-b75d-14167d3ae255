/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.1);
}

/* Header */
.header {
    background: #2c3e50;
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 1.8rem;
    font-weight: 600;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #95a5a6;
    transition: background-color 0.3s ease;
}

.status-dot.online {
    background: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.status-dot.offline {
    background: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
}

/* Tab Navigation */
.tab-nav {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 2px solid #ecf0f1;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: #7f8c8d;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    color: #2c3e50;
    background: rgba(52, 73, 94, 0.05);
}

.tab-btn.active {
    color: #2c3e50;
    border-bottom-color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.section {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.section h3 {
    color: #34495e;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
}

.help-text {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #3498db;
}

/* Drop Zone */
.drop-zone {
    border: 3px dashed #bdc3c7;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drop-zone:hover,
.drop-zone.drag-over {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.drop-zone-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.drop-icon {
    font-size: 3rem;
    opacity: 0.6;
}

.drop-zone p {
    color: #7f8c8d;
    font-size: 1.1rem;
}

.link-btn {
    background: none;
    border: none;
    color: #3498db;
    text-decoration: underline;
    cursor: pointer;
    font-size: inherit;
}

.link-btn:hover {
    color: #2980b9;
}

/* Separator */
.separator {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #ecf0f1;
}

.separator span {
    background: white;
    padding: 0 1rem;
    color: #95a5a6;
    font-weight: 500;
}

/* Input Groups */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #34495e;
    font-weight: 500;
}

.path-input,
.text-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.path-input:focus,
.text-input:focus {
    outline: none;
    border-color: #3498db;
}

.path-input.has-selection {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.05);
}

.path-input.has-selection::placeholder {
    color: #27ae60;
}

.input-group {
    display: flex;
    gap: 0.5rem;
    align-items: end;
    flex-wrap: wrap;
}

.input-group input[type="text"] {
    flex: 1;
    min-width: 200px;
}

.input-group input[type="file"] {
    display: none;
}

.input-group .btn {
    flex-shrink: 0;
}

/* Options */
.options-section {
    margin: 2rem 0;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: #34495e;
    font-weight: 500;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #bdc3c7;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Buttons */
.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Action Section */
.action-section {
    text-align: center;
    margin: 2rem 0;
}

/* Progress */
.progress-section {
    margin: 2rem 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 4px;
    animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

.progress-text {
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}

/* Results */
.results-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #27ae60;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.result-item {
    background: white;
    padding: 1rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.result-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 0.25rem;
}

.result-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Compression Visualization */
.compression-visual {
    grid-column: 1 / -1; /* Span full width */
}

.compression-bar {
    position: relative;
    width: 100%;
    height: 30px;
    background: #ecf0f1;
    border-radius: 15px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.compression-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 15px;
    transition: width 1s ease-in-out;
    position: relative;
}

.compression-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

/* Copy Button */
.copy-container {
    display: flex;
    justify-content: center;
    grid-column: 1 / -1;
}

.copy-btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

/* File List */
.file-list {
    margin-top: 1rem;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0.5rem;
}

.file-name {
    font-weight: 500;
    color: #2c3e50;
}

.file-size {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Archive Contents */
.archive-contents {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.archive-item {
    padding: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.archive-item:last-child {
    border-bottom: none;
}

.archive-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.archive-note {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.archive-note p {
    margin: 0.5rem 0;
    color: #1565c0;
}

.archive-note p:first-child {
    font-weight: 600;
    margin-bottom: 0.75rem;
}

/* Footer */
.footer {
    background: #34495e;
    color: white;
    padding: 1rem 2rem;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-messages {
    flex: 1;
}

.footer-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    text-align: right;
}

.api-info,
.shortcuts-info {
    font-size: 0.8rem;
    opacity: 0.8;
}

.shortcuts-info {
    font-style: italic;
}

/* Path Browse Menu */
.path-browse-menu {
    background: white;
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    min-width: 180px;
}

.menu-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid #ecf0f1;
    font-size: 0.9rem;
    color: #2c3e50;
}

.menu-item:last-child {
    border-bottom: none;
}

.menu-item:hover {
    background: #f8f9fa;
    color: #3498db;
}

/* Path Helper */
.path-helper {
    margin-top: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
}

.helper-header {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
}

.helper-examples {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.helper-example {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.helper-example:hover {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.helper-example code {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: #2c3e50;
    background: none;
    padding: 0;
}

.helper-copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.helper-copy-btn:hover {
    background: rgba(52, 152, 219, 0.1);
}

.helper-note {
    font-size: 0.8rem;
    color: #6c757d;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .main-content {
        padding: 1rem;
    }
    
    .section {
        padding: 1.5rem;
    }
    
    .header-content,
    .footer-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .tab-nav {
        flex-direction: column;
    }
    
    .options-grid {
        grid-template-columns: 1fr;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: #27ae60;
}

.text-error {
    color: #e74c3c;
}

.text-warning {
    color: #f39c12;
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
